package container

import (
	"gorm.io/gorm"
	
	"shikeyinxiang/internal/api/controllers"
	"shikeyinxiang/internal/application/service"
	"shikeyinxiang/internal/domain/repositories"
	"shikeyinxiang/internal/infrastructure/repositories"
)

// Container 依赖注入容器
type Container struct {
	db *gorm.DB
	
	// Repositories
	userRepo repositories.UserRepository
	
	// Services
	authService *service.AuthServiceImpl
	
	// Controllers
	authController *controllers.AuthController
}

// NewContainer 创建新的容器实例
func NewContainer(db *gorm.DB) *Container {
	container := &Container{
		db: db,
	}
	
	// 初始化依赖关系
	container.initRepositories()
	container.initServices()
	container.initControllers()
	
	return container
}

// initRepositories 初始化仓储层
func (c *Container) initRepositories() {
	c.userRepo = repositories.NewUserRepository(c.db)
}

// initServices 初始化服务层
func (c *Container) initServices() {
	c.authService = service.NewAuthService(c.userRepo)
}

// initControllers 初始化控制器层
func (c *Container) initControllers() {
	c.authController = controllers.NewAuthController(c.authService)
}

// GetAuthController 获取认证控制器
func (c *Container) GetAuthController() *controllers.AuthController {
	return c.authController
}

// GetUserRepository 获取用户仓储
func (c *Container) GetUserRepository() repositories.UserRepository {
	return c.userRepo
}

// GetAuthService 获取认证服务
func (c *Container) GetAuthService() *service.AuthServiceImpl {
	return c.authService
}
