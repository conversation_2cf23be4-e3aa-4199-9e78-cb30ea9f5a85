package repositories

import (
	"errors"
	"gorm.io/gorm"
	"shikeyinxiang/internal/domain/entities"
	"shikeyinxiang/internal/domain/repositories"
	"shikeyinxiang/internal/infrastructure/persistence"
)

// UserRepositoryImpl 用户仓储实现
type UserRepositoryImpl struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository(db *gorm.DB) repositories.UserRepository {
	return &UserRepositoryImpl{
		db: db,
	}
}

// Create 创建新用户
func (r *UserRepositoryImpl) Create(user *entities.User) error {
	userModel := &persistence.UserModel{}
	userModel.FromEntity(user)
	
	if err := r.db.Create(userModel).Error; err != nil {
		return err
	}
	
	// 更新实体的ID
	user.ID = userModel.ID
	return nil
}

// GetByID 根据ID获取用户
func (r *UserRepositoryImpl) GetByID(id int64) (*entities.User, error) {
	var userModel persistence.UserModel
	if err := r.db.Where("id = ?", id).First(&userModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return userModel.ToEntity(), nil
}

// GetByUsername 根据用户名获取用户
func (r *UserRepositoryImpl) GetByUsername(username string) (*entities.User, error) {
	var userModel persistence.UserModel
	if err := r.db.Where("username = ?", username).First(&userModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return userModel.ToEntity(), nil
}

// GetByEmail 根据邮箱获取用户
func (r *UserRepositoryImpl) GetByEmail(email string) (*entities.User, error) {
	var userModel persistence.UserModel
	if err := r.db.Where("email = ?", email).First(&userModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return userModel.ToEntity(), nil
}

// GetByUsernameOrEmail 根据用户名或邮箱获取用户（用于登录）
func (r *UserRepositoryImpl) GetByUsernameOrEmail(usernameOrEmail string) (*entities.User, error) {
	var userModel persistence.UserModel
	if err := r.db.Where("username = ? OR email = ?", usernameOrEmail, usernameOrEmail).First(&userModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return userModel.ToEntity(), nil
}

// Update 更新用户信息
func (r *UserRepositoryImpl) Update(user *entities.User) error {
	userModel := &persistence.UserModel{}
	userModel.FromEntity(user)
	return r.db.Save(userModel).Error
}

// Delete 删除用户（软删除）
func (r *UserRepositoryImpl) Delete(id int64) error {
	return r.db.Model(&persistence.UserModel{}).Where("id = ?", id).Update("status", 0).Error
}

// ExistsByUsername 检查用户名是否已存在
func (r *UserRepositoryImpl) ExistsByUsername(username string) (bool, error) {
	var count int64
	if err := r.db.Model(&persistence.UserModel{}).Where("username = ?", username).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// ExistsByEmail 检查邮箱是否已存在
func (r *UserRepositoryImpl) ExistsByEmail(email string) (bool, error) {
	var count int64
	if err := r.db.Model(&persistence.UserModel{}).Where("email = ?", email).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// List 获取用户列表（分页）
func (r *UserRepositoryImpl) List(offset, limit int) ([]*entities.User, int64, error) {
	var userModels []persistence.UserModel
	var total int64
	
	// 获取总数
	if err := r.db.Model(&persistence.UserModel{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	if err := r.db.Offset(offset).Limit(limit).Find(&userModels).Error; err != nil {
		return nil, 0, err
	}
	
	// 转换为实体
	users := make([]*entities.User, len(userModels))
	for i, userModel := range userModels {
		users[i] = userModel.ToEntity()
	}
	
	return users, total, nil
}

// UpdateStatus 更新用户状态
func (r *UserRepositoryImpl) UpdateStatus(id int64, status int8) error {
	return r.db.Model(&persistence.UserModel{}).Where("id = ?", id).Update("status", status).Error
}
