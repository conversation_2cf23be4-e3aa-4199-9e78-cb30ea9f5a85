package routes

import (
	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/controllers"
	"shikeyinxiang/internal/database"
	"shikeyinxiang/internal/repositories_impl"
	"shikeyinxiang/internal/logic"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/service"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine) {
	// 应用全局中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.ErrorHandlerMiddleware())
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RequestIDMiddleware())

	// 设置404和405处理器
	r.NoRoute(middleware.NotFoundHandler())
	r.NoMethod(middleware.MethodNotAllowedHandler())

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Service is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由 /api/jwt
		setupAuthRoutes(api)

		// TODO: 其他路由将在后续实现
		// setupUserRoutes(api)
		// setupFoodRoutes(api)
		// setupDietRecordRoutes(api)
		// setupNutritionRoutes(api)
		// setupFileRoutes(api)
		// setupAdminRoutes(api)
	}
}

// setupAuthRoutes 设置认证路由
func setupAuthRoutes(api *gin.RouterGroup) {
	// 初始化服务依赖
	userRepo := repositories_impl.NewUserRepository(database.GetDB())
	service.Auth = logic.NewAuth(userRepo)

	// 创建控制器
	authController := controllers.NewAuthController()

	auth := api.Group("/jwt")
	{
		// 公开路由（无需认证）
		auth.POST("/login", authController.Login)       // 用户登录
		auth.POST("/register", authController.Register) // 用户注册

		// 需要认证的路由
		auth.POST("/logout", middleware.AuthMiddleware(), authController.Logout)        // 用户登出
		auth.POST("/refresh", middleware.AuthMiddleware(), authController.RefreshToken) // 刷新令牌
		auth.GET("/me", middleware.AuthMiddleware(), authController.GetCurrentUser)     // 获取当前用户信息
	}
}
