package dto

import "time"

// LoginRequest 登录请求 DTO
type LoginRequest struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"` // 用户名或邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"` // 密码
}

// RegisterRequest 注册请求 DTO
type RegisterRequest struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"`   // 用户名
	Email    string `json:"email" binding:"required" validate:"required,email,max=100"`     // 邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"`  // 密码
}

// LoginResponse 登录响应 DTO
type LoginResponse struct {
	User  *UserInfo `json:"user"`  // 用户信息
	Token string    `json:"token"` // JWT令牌
}

// UserInfo 用户信息 DTO（不包含敏感信息）
type UserInfo struct {
	ID         int64     `json:"id"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	Role       string    `json:"role"`
	Status     int8      `json:"status"`
	AvatarURL  string    `json:"avatarUrl,omitempty"`
	CreateTime time.Time `json:"createTime"`
}

// RefreshTokenResponse 刷新令牌响应 DTO
type RefreshTokenResponse struct {
	Token string `json:"token"`
}
