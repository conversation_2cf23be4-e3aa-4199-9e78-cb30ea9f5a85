package routes

import (
	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/api/controllers"
	"shikeyinxiang/internal/api/middleware"
	"shikeyinxiang/internal/application/service"
	"shikeyinxiang/internal/infrastructure/database"
	"shikeyinxiang/internal/infrastructure/repositories"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine) {
	// 应用全局中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.ErrorHandlerMiddleware())
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RequestIDMiddleware())

	// 设置404和405处理器
	r.NoRoute(middleware.NotFoundHandler())
	r.NoMethod(middleware.MethodNotAllowedHandler())

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Service is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由 /api/auth
		setupAuthRoutes(api)

		// 用户相关路由 /api/user (需要认证)
		setupUserRoutes(api)

		// 食物相关路由 /api/food
		setupFoodRoutes(api)

		// 饮食记录相关路由 /api/diet-records (需要认证)
		setupDietRecordRoutes(api)

		// 营养分析相关路由 /api/nutrition (需要认证)
		setupNutritionRoutes(api)

		// 文件相关路由 /api/files
		setupFileRoutes(api)

		// 管理员相关路由 /api/admin (需要管理员权限)
		setupAdminRoutes(api)
	}
}

// setupAuthRoutes 设置认证路由
func setupAuthRoutes(api *gin.RouterGroup) {
	// 创建依赖链
	userRepo := repositories.NewUserRepository(database.GetDB())
	authService := service.NewAuthService(userRepo)
	authController := controllers.NewAuthController(authService)

	auth := api.Group("/auth")
	{
		// 公开路由（无需认证）
		auth.POST("/login", authController.Login)       // 用户登录
		auth.POST("/register", authController.Register) // 用户注册

		// 需要认证的路由
		auth.POST("/logout", middleware.AuthMiddleware(), authController.Logout)           // 用户登出
		auth.POST("/refresh", middleware.AuthMiddleware(), authController.RefreshToken)    // 刷新令牌
		auth.GET("/me", middleware.AuthMiddleware(), authController.GetCurrentUser)        // 获取当前用户信息
	}
}

// setupUserRoutes 设置用户路由
func setupUserRoutes(api *gin.RouterGroup) {
	user := api.Group("/user")
	user.Use(middleware.AuthMiddleware()) // 需要认证
	{
		// TODO: 添加用户相关路由
		// user.GET("/profile", userController.GetProfile)
		// user.PUT("/profile", userController.UpdateProfile)
		// user.POST("/change-password", userController.ChangePassword)
		// user.POST("/avatar/upload-url", userController.GenerateAvatarUploadURL)
		// user.GET("/avatar", userController.GetAvatarURL)
	}
}

// setupFoodRoutes 设置食物路由
func setupFoodRoutes(api *gin.RouterGroup) {
	food := api.Group("/food")
	{
		// TODO: 添加食物相关路由
		// food.GET("/list", foodController.GetFoodList)
		// food.GET("/search", foodController.SearchFoods)
		// food.GET("/categories", foodController.GetCategories)
		// food.GET("/:id", foodController.GetFoodDetail)
	}
}

// setupDietRecordRoutes 设置饮食记录路由
func setupDietRecordRoutes(api *gin.RouterGroup) {
	dietRecords := api.Group("/diet-records")
	dietRecords.Use(middleware.AuthMiddleware()) // 需要认证
	{
		// TODO: 添加饮食记录相关路由
		// dietRecords.POST("", dietRecordController.AddDietRecord)
		// dietRecords.GET("", dietRecordController.GetDietRecords)
		// dietRecords.PUT("/:id", dietRecordController.UpdateDietRecord)
		// dietRecords.DELETE("/:id", dietRecordController.DeleteDietRecord)
	}
}

// setupNutritionRoutes 设置营养分析路由
func setupNutritionRoutes(api *gin.RouterGroup) {
	nutrition := api.Group("/nutrition")
	nutrition.Use(middleware.AuthMiddleware()) // 需要认证
	{
		// TODO: 添加营养分析相关路由
		// nutrition.GET("/daily", nutritionController.GetDailyNutrition)
		// nutrition.POST("/goals", nutritionController.SetNutritionGoals)
		// nutrition.GET("/advice", nutritionController.GetNutritionAdvice)
	}
}

// setupFileRoutes 设置文件路由
func setupFileRoutes(api *gin.RouterGroup) {
	files := api.Group("/files")
	{
		// TODO: 添加文件相关路由
		// files.POST("/upload-url", middleware.AuthMiddleware(), fileController.GenerateUploadURL)
	}
}

// setupAdminRoutes 设置管理员路由
func setupAdminRoutes(api *gin.RouterGroup) {
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware())      // 需要认证
	admin.Use(middleware.AdminOnlyMiddleware()) // 需要管理员权限
	{
		// 用户管理 /api/admin/users
		adminUsers := admin.Group("/users")
		{
			// TODO: 添加管理员用户管理路由
			// adminUsers.GET("", adminUserController.ListUsers)
			// adminUsers.POST("", adminUserController.AddUser)
			// adminUsers.PUT("/:id", adminUserController.UpdateUser)
			// adminUsers.DELETE("/:id", adminUserController.DeleteUser)
		}

		// 食物管理 /api/admin/food
		adminFood := admin.Group("/food")
		{
			// TODO: 添加管理员食物管理路由
			// adminFood.GET("/list", adminFoodController.GetFoodList)
			// adminFood.POST("", adminFoodController.CreateFood)
			// adminFood.PUT("/:id", adminFoodController.UpdateFood)
			// adminFood.DELETE("/:id", adminFoodController.DeleteFood)
			// adminFood.GET("/upload-image-url", adminFoodController.GetUploadImageURL)
		}

		// 饮食记录管理 /api/admin/diet-records
		adminDietRecords := admin.Group("/diet-records")
		{
			// TODO: 添加管理员饮食记录管理路由
			// adminDietRecords.GET("", adminDietRecordController.GetDietRecords)
			// adminDietRecords.DELETE("/:id", adminDietRecordController.DeleteDietRecord)
		}

		// 仪表盘统计 /api/admin/dashboard
		adminDashboard := admin.Group("/dashboard")
		{
			// TODO: 添加仪表盘相关路由
			// adminDashboard.GET("/stats", dashboardController.GetStats)
			// adminDashboard.GET("/user-stats", dashboardController.GetUserStats)
			// adminDashboard.GET("/diet-stats", dashboardController.GetDietStats)
		}
	}
}
