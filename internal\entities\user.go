package entities

import (
	"github.com/shopspring/decimal"
	"shikeyinxiang/internal/consts"
	"time"
)

// User 用户实体 - 包含业务逻辑和数据库映射
type User struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Username   string    `gorm:"column:username;type:varchar(50);not null;uniqueIndex" json:"username"`
	Password   string    `gorm:"column:password;type:varchar(100);not null" json:"-"` // 不返回密码
	Email      string    `gorm:"column:email;type:varchar(100);not null;uniqueIndex" json:"email"`
	Role       string    `gorm:"column:role;type:varchar(20);not null;default:USER" json:"role"`
	Status     int8      `gorm:"column:status;type:tinyint(1);not null;default:1;comment:1表示用户启动，0表示用户被封禁" json:"status"`
	CreateTime time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`
	OpenID     *string   `gorm:"column:openid;type:varchar(100);uniqueIndex:idx_user_openid;comment:微信openid，用于唯一标识用户" json:"openid,omitempty"`
	AvatarURL  *string   `gorm:"column:avatar_url;type:varchar(255);comment:用户头像URL" json:"avatarUrl,omitempty"`
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// NewUser 创建新用户实体
func NewUser(username, email, password, role string) *User {
	return &User{
		Username:   username,
		Email:      email,
		Password:   password,
		Role:       role,
		Status:     consts.UserStatusActive, // 默认启用
		CreateTime: time.Now(),
	}
}

// IsActive 检查用户是否处于活跃状态
func (u *User) IsActive() bool {
	return u.Status == consts.UserStatusActive
}

// IsAdmin 检查用户是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == consts.RoleAdmin
}

// SetAvatarURL 设置用户头像URL
func (u *User) SetAvatarURL(url string) {
	u.AvatarURL = &url
}

// UserNutritionGoals 用户营养目标实体 - 包含业务逻辑和数据库映射
type UserNutritionGoals struct {
	ID             int64            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID         int64            `gorm:"column:user_id;not null" json:"userId"`
	CalorieTarget  *int             `gorm:"column:calorie_target" json:"calorieTarget"`
	WeightTarget   *decimal.Decimal `gorm:"column:weight_target;type:decimal(5,2)" json:"weightTarget"`
	ProteinTarget  *int             `gorm:"column:protein_target" json:"proteinTarget"`
	CarbsTarget    *int             `gorm:"column:carbs_target" json:"carbsTarget"`
	FatTarget      *int             `gorm:"column:fat_target" json:"fatTarget"`
	IsVegetarian   bool             `gorm:"column:is_vegetarian;type:tinyint(1);default:0" json:"isVegetarian"`
	IsLowCarb      bool             `gorm:"column:is_low_carb;type:tinyint(1);default:0" json:"isLowCarb"`
	IsHighProtein  bool             `gorm:"column:is_high_protein;type:tinyint(1);default:0" json:"isHighProtein"`
	IsGlutenFree   bool             `gorm:"column:is_gluten_free;type:tinyint(1);default:0" json:"isGlutenFree"`
	IsLowSodium    bool             `gorm:"column:is_low_sodium;type:tinyint(1);default:0" json:"isLowSodium"`
	CreatedAt      *time.Time       `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt      *time.Time       `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`
}

// TableName 指定表名
func (UserNutritionGoals) TableName() string {
	return "user_nutrition_goals"
}

// NewUserNutritionGoals 创建新的用户营养目标
func NewUserNutritionGoals(userID int64) *UserNutritionGoals {
	now := time.Now()
	return &UserNutritionGoals{
		UserID:    userID,
		CreatedAt: &now,
		UpdatedAt: &now,
	}
}

// SetCalorieTarget 设置卡路里目标
func (ung *UserNutritionGoals) SetCalorieTarget(target int) {
	ung.CalorieTarget = &target
	now := time.Now()
	ung.UpdatedAt = &now
}

// SetWeightTarget 设置体重目标
func (ung *UserNutritionGoals) SetWeightTarget(target decimal.Decimal) {
	ung.WeightTarget = &target
	now := time.Now()
	ung.UpdatedAt = &now
}
