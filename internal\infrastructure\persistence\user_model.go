package persistence

import (
	"time"
	"github.com/shopspring/decimal"
	"shikeyinxiang/internal/domain/entities"
)

// UserModel 用户持久化模型 - 包含 GORM 标签和数据库映射
type UserModel struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Username   string    `gorm:"column:username;type:varchar(50);not null;uniqueIndex" json:"username"`
	Password   string    `gorm:"column:password;type:varchar(100);not null" json:"-"` // 不返回密码
	Email      string    `gorm:"column:email;type:varchar(100);not null;uniqueIndex" json:"email"`
	Role       string    `gorm:"column:role;type:varchar(20);not null;default:USER" json:"role"`
	Status     int8      `gorm:"column:status;type:tinyint(1);not null;default:1;comment:1表示用户启动，0表示用户被封禁" json:"status"`
	CreateTime time.Time `gorm:"column:create_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`
	OpenID     *string   `gorm:"column:openid;type:varchar(100);uniqueIndex:idx_user_openid;comment:微信openid，用于唯一标识用户" json:"openid,omitempty"`
	AvatarURL  *string   `gorm:"column:avatar_url;type:varchar(255);comment:用户头像URL" json:"avatarUrl,omitempty"`
}

// TableName 指定表名
func (UserModel) TableName() string {
	return "user"
}

// ToEntity 将持久化模型转换为领域实体
func (um *UserModel) ToEntity() *entities.User {
	return &entities.User{
		ID:         um.ID,
		Username:   um.Username,
		Password:   um.Password,
		Email:      um.Email,
		Role:       um.Role,
		Status:     um.Status,
		CreateTime: um.CreateTime,
		OpenID:     um.OpenID,
		AvatarURL:  um.AvatarURL,
	}
}

// FromEntity 从领域实体创建持久化模型
func (um *UserModel) FromEntity(user *entities.User) {
	um.ID = user.ID
	um.Username = user.Username
	um.Password = user.Password
	um.Email = user.Email
	um.Role = user.Role
	um.Status = user.Status
	um.CreateTime = user.CreateTime
	um.OpenID = user.OpenID
	um.AvatarURL = user.AvatarURL
}

// UserNutritionGoalsModel 用户营养目标持久化模型
type UserNutritionGoalsModel struct {
	ID             int64            `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID         int64            `gorm:"column:user_id;not null" json:"userId"`
	CalorieTarget  *int             `gorm:"column:calorie_target" json:"calorieTarget"`
	WeightTarget   *decimal.Decimal `gorm:"column:weight_target;type:decimal(5,2)" json:"weightTarget"`
	ProteinTarget  *int             `gorm:"column:protein_target" json:"proteinTarget"`
	CarbsTarget    *int             `gorm:"column:carbs_target" json:"carbsTarget"`
	FatTarget      *int             `gorm:"column:fat_target" json:"fatTarget"`
	IsVegetarian   bool             `gorm:"column:is_vegetarian;type:tinyint(1);default:0" json:"isVegetarian"`
	IsLowCarb      bool             `gorm:"column:is_low_carb;type:tinyint(1);default:0" json:"isLowCarb"`
	IsHighProtein  bool             `gorm:"column:is_high_protein;type:tinyint(1);default:0" json:"isHighProtein"`
	IsGlutenFree   bool             `gorm:"column:is_gluten_free;type:tinyint(1);default:0" json:"isGlutenFree"`
	IsLowSodium    bool             `gorm:"column:is_low_sodium;type:tinyint(1);default:0" json:"isLowSodium"`
	CreatedAt      *time.Time       `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"createdAt"`
	UpdatedAt      *time.Time       `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updatedAt"`
}

// TableName 指定表名
func (UserNutritionGoalsModel) TableName() string {
	return "user_nutrition_goals"
}

// ToEntity 将持久化模型转换为领域实体
func (ungm *UserNutritionGoalsModel) ToEntity() *entities.UserNutritionGoals {
	return &entities.UserNutritionGoals{
		ID:             ungm.ID,
		UserID:         ungm.UserID,
		CalorieTarget:  ungm.CalorieTarget,
		WeightTarget:   ungm.WeightTarget,
		ProteinTarget:  ungm.ProteinTarget,
		CarbsTarget:    ungm.CarbsTarget,
		FatTarget:      ungm.FatTarget,
		IsVegetarian:   ungm.IsVegetarian,
		IsLowCarb:      ungm.IsLowCarb,
		IsHighProtein:  ungm.IsHighProtein,
		IsGlutenFree:   ungm.IsGlutenFree,
		IsLowSodium:    ungm.IsLowSodium,
		CreatedAt:      ungm.CreatedAt,
		UpdatedAt:      ungm.UpdatedAt,
	}
}

// FromEntity 从领域实体创建持久化模型
func (ungm *UserNutritionGoalsModel) FromEntity(goals *entities.UserNutritionGoals) {
	ungm.ID = goals.ID
	ungm.UserID = goals.UserID
	ungm.CalorieTarget = goals.CalorieTarget
	ungm.WeightTarget = goals.WeightTarget
	ungm.ProteinTarget = goals.ProteinTarget
	ungm.CarbsTarget = goals.CarbsTarget
	ungm.FatTarget = goals.FatTarget
	ungm.IsVegetarian = goals.IsVegetarian
	ungm.IsLowCarb = goals.IsLowCarb
	ungm.IsHighProtein = goals.IsHighProtein
	ungm.IsGlutenFree = goals.IsGlutenFree
	ungm.IsLowSodium = goals.IsLowSodium
	ungm.CreatedAt = goals.CreatedAt
	ungm.UpdatedAt = goals.UpdatedAt
}
