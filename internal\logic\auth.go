package logic

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"golang.org/x/crypto/bcrypt"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/domain/entities"
	"shikeyinxiang/internal/domain/repositories"
	"shikeyinxiang/internal/infrastructure/auth"
	"shikeyinxiang/internal/service"
)

// sAuth 认证业务逻辑实现
type sAuth struct {
	userRepo repositories.UserRepository
}

// NewAuth 创建认证服务实例
func NewAuth(userRepo repositories.UserRepository) service.IAuth {
	return &sAuth{
		userRepo: userRepo,
	}
}

// Login 用户登录
func (s *sAuth) Login(ctx context.Context, req *v1.LoginReq) (*v1.LoginRes, error) {
	// 查找用户（支持用户名或邮箱登录）
	user, err := s.userRepo.GetByUsernameOrEmail(req.Username)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, errors.New(consts.MsgUserNotFound)
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, errors.New(consts.MsgUserDisabled)
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.New(consts.MsgPasswordError)
	}

	// 生成JWT令牌
	token, err := auth.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	// 构建响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		CreateTime: user.CreateTime,
	}

	if user.AvatarURL != nil {
		userInfo.AvatarURL = *user.AvatarURL
	}

	return &v1.LoginRes{
		User:  userInfo,
		Token: token,
	}, nil
}

// Register 用户注册
func (s *sAuth) Register(ctx context.Context, req *v1.RegisterReq) (*v1.RegisterRes, error) {
	// 检查用户名是否已存在
	exists, err := s.userRepo.ExistsByUsername(req.Username)
	if err != nil {
		return nil, fmt.Errorf("检查用户名失败: %w", err)
	}
	if exists {
		return nil, errors.New(consts.MsgUsernameExists)
	}

	// 检查邮箱是否已存在
	exists, err = s.userRepo.ExistsByEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	if exists {
		return nil, errors.New(consts.MsgEmailExists)
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建新用户实体
	newUser := entities.NewUser(
		req.Username,
		req.Email,
		string(hashedPassword),
		consts.RoleUser, // 默认角色为普通用户
	)

	// 保存到数据库
	if err := s.userRepo.Create(newUser); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 构建响应（不包含密码）
	userInfo := &v1.UserInfo{
		ID:         newUser.ID,
		Username:   newUser.Username,
		Email:      newUser.Email,
		Role:       newUser.Role,
		Status:     newUser.Status,
		CreateTime: newUser.CreateTime,
	}

	return &v1.RegisterRes{
		User: userInfo,
	}, nil
}

// Logout 用户登出
func (s *sAuth) Logout(ctx context.Context, token string) error {
	// 移除Bearer前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 将令牌加入黑名单
	if err := auth.BlacklistToken(token); err != nil {
		return fmt.Errorf("登出失败: %w", err)
	}

	return nil
}

// RefreshToken 刷新令牌
func (s *sAuth) RefreshToken(ctx context.Context, token string) (string, error) {
	// 移除Bearer前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 解析当前令牌
	claims, err := auth.ParseToken(token)
	if err != nil {
		return "", fmt.Errorf("令牌解析失败: %w", err)
	}

	// 验证用户是否仍然存在且状态正常
	user, err := s.userRepo.GetByID(claims.UserID)
	if err != nil {
		return "", fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil || !user.IsActive() {
		return "", errors.New("用户不存在或已被禁用")
	}

	// 将旧令牌加入黑名单
	if err := auth.BlacklistToken(token); err != nil {
		return "", fmt.Errorf("旧令牌失效失败: %w", err)
	}

	// 生成新令牌
	newToken, err := auth.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return "", fmt.Errorf("生成新令牌失败: %w", err)
	}

	return newToken, nil
}
