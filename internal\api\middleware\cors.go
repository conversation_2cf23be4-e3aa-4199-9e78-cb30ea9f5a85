package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/config"
)

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg := config.AppConfig.CORS

		origin := c.Request.Header.Get("Origin")

		// 检查是否允许该来源
		if isAllowedOrigin(origin, cfg.AllowOrigins) {
			c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
		} else if contains(cfg.AllowOrigins, "*") {
			c.<PERSON>er("Access-Control-Allow-Origin", "*")
		}

		// 设置允许的方法
		c.Header("Access-Control-Allow-Methods", strings.Join(cfg.AllowMethods, ", "))

		// 设置允许的头部
		c.Header("Access-Control-Allow-Headers", strings.Join(cfg.AllowHeaders, ", "))

		// 设置是否允许携带凭证
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		// 设置预检请求的缓存时间
		c.Head<PERSON>("Access-Control-Max-Age", "86400")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// isAllowedOrigin 检查来源是否被允许
func isAllowedOrigin(origin string, allowedOrigins []string) bool {
	if origin == "" {
		return false
	}

	for _, allowed := range allowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}

		// 支持通配符匹配（简单实现）
		//匹配前缀（比如http://localhost*匹配http://localhost:8080或者http://localhost:8081，......)
		if strings.HasSuffix(allowed, "*") {
			prefix := strings.TrimSuffix(allowed, "*")
			if strings.HasPrefix(origin, prefix) {
				return true
			}
		}
	}

	return false
}

// contains 检查字符串切片是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
