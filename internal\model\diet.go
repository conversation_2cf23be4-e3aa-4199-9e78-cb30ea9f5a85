package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// DietRecord 饮食记录主表模型 - 完全匹配数据库表结构
type DietRecord struct {
	ID           int64           `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID       int64           `gorm:"column:user_id;not null;index:idx_user_date;comment:用户ID" json:"userId"`
	Date         time.Time       `gorm:"column:date;type:date;not null;index:idx_user_date;comment:记录日期" json:"date"`
	Time         time.Time       `gorm:"column:time;type:time;not null;comment:记录时间" json:"time"`
	MealType     string          `gorm:"column:meal_type;type:varchar(20);not null;comment:餐次类型: breakfast/lunch/dinner/snacks" json:"mealType"`
	Remark       *string         `gorm:"column:remark;type:varchar(200);comment:备注信息" json:"remark,omitempty"`
	TotalCalorie decimal.Decimal `gorm:"column:total_calorie;type:decimal(8,2);not null;comment:总热量(千卡)" json:"totalCalorie"`
	CreatedAt    time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt    time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
	
	// 关联关系
	Foods []DietRecordFood `gorm:"foreignKey:DietRecordID;references:ID" json:"foods,omitempty"`
}

// TableName 指定表名
func (DietRecord) TableName() string {
	return "diet_records"
}

// DietRecordFood 饮食记录食物明细表模型 - 完全匹配数据库表结构
type DietRecordFood struct {
	ID           int64           `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	DietRecordID int64           `gorm:"column:diet_record_id;not null;index:idx_record_id;comment:关联饮食记录ID" json:"dietRecordId"`
	FoodID       int64           `gorm:"column:food_id;not null;comment:食物ID" json:"foodId"`
	FoodName     string          `gorm:"column:food_name;type:varchar(50);not null;comment:食物名称" json:"foodName"`
	Amount       decimal.Decimal `gorm:"column:amount;type:decimal(8,2);not null;comment:食物数量" json:"amount"`
	Unit         string          `gorm:"column:unit;type:varchar(20);not null;comment:计量单位" json:"unit"`
	Calories     decimal.Decimal `gorm:"column:calories;type:decimal(8,2);not null;comment:热量(千卡)" json:"calories"`
	Protein      decimal.Decimal `gorm:"column:protein;type:decimal(8,2);not null;comment:蛋白质(g)" json:"protein"`
	Fat          decimal.Decimal `gorm:"column:fat;type:decimal(8,2);not null;comment:脂肪(g)" json:"fat"`
	Carbs        decimal.Decimal `gorm:"column:carbs;type:decimal(8,2);not null;comment:碳水化合物(g)" json:"carbs"`
	Grams        decimal.Decimal `gorm:"column:grams;type:decimal(8,2);not null;comment:食物克数" json:"grams"`
	CreatedAt    time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	
	// 关联关系
	Food *Food `gorm:"foreignKey:FoodID;references:ID" json:"food,omitempty"`
}

// TableName 指定表名
func (DietRecordFood) TableName() string {
	return "diet_record_foods"
}
