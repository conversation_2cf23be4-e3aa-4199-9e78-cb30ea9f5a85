package repositories

import (
	"shikeyinxiang/internal/entities"
)

// UserRepository 用户仓储接口 - 定义数据访问抽象
type UserRepository interface {
	// Create 创建新用户
	Create(user *entities.User) error

	// GetByID 根据ID获取用户
	GetByID(id int64) (*entities.User, error)

	// GetByUsername 根据用户名获取用户
	GetByUsername(username string) (*entities.User, error)

	// GetByEmail 根据邮箱获取用户
	GetByEmail(email string) (*entities.User, error)

	// GetByUsernameOrEmail 根据用户名或邮箱获取用户（用于登录）
	GetByUsernameOrEmail(usernameOrEmail string) (*entities.User, error)

	// Update 更新用户信息
	Update(user *entities.User) error

	// Delete 删除用户（软删除）
	Delete(id int64) error

	// ExistsByUsername 检查用户名是否已存在
	ExistsByUsername(username string) (bool, error)

	// ExistsByEmail 检查邮箱是否已存在
	ExistsByEmail(email string) (bool, error)

	// List 获取用户列表（分页）
	List(offset, limit int) ([]*entities.User, int64, error)

	// UpdateStatus 更新用户状态
	UpdateStatus(id int64, status int8) error
}
