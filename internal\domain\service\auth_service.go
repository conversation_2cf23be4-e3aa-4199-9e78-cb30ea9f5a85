package service

import "shikeyinxiang/internal/dto"

// AuthService 认证领域服务接口 - 定义认证相关的业务逻辑
type AuthService interface {
	// Login 用户登录
	// loginRequest: 登录请求，包含用户名/邮箱和密码
	// 返回: 用户信息、JWT令牌和错误
	Login(loginRequest *dto.LoginRequest) (*dto.LoginResponse, error)

	// Register 用户注册
	// registerRequest: 注册请求，包含用户名、邮箱、密码等信息
	// 返回: 注册成功的用户信息和错误
	Register(registerRequest *dto.RegisterRequest) (*dto.UserInfo, error)

	// Logout 用户登出
	// token: JWT令牌
	// 返回: 错误信息
	Logout(token string) error

	// RefreshToken 刷新令牌
	// token: 当前JWT令牌
	// 返回: 新的JWT令牌和错误
	RefreshToken(token string) (string, error)
}
