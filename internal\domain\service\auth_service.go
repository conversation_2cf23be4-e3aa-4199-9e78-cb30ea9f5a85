package service

import "shikeyinxiang/internal/model"

// AuthService 认证服务接口
type AuthService interface {
	// Login 用户登录
	// loginRequest: 登录请求，包含用户名/邮箱和密码
	// 返回: 用户信息、JWT令牌和错误
	Login(loginRequest *LoginRequest) (*LoginResponse, error)

	// Register 用户注册
	// registerRequest: 注册请求，包含用户名、邮箱、密码等信息
	// 返回: 注册成功的用户信息和错误
	Register(registerRequest *RegisterRequest) (*model.User, error)

	// Logout 用户登出
	// token: JWT令牌
	// 返回: 错误信息
	Logout(token string) error

	// RefreshToken 刷新令牌
	// token: 当前JWT令牌
	// 返回: 新的JWT令牌和错误
	RefreshToken(token string) (string, error)
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"` // 用户名或邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"` // 密码
}

// RegisterRequest 注册请求结构
type RegisterRequest struct {
	Username string `json:"username" binding:"required" validate:"required,min=3,max=50"`   // 用户名
	Email    string `json:"email" binding:"required" validate:"required,email,max=100"`     // 邮箱
	Password string `json:"password" binding:"required" validate:"required,min=6,max=100"`  // 密码
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	User  *UserInfo `json:"user"`  // 用户信息
	Token string    `json:"token"` // JWT令牌
}

// UserInfo 用户信息结构（不包含敏感信息）
type UserInfo struct {
	ID         int64  `json:"id"`
	Username   string `json:"username"`
	Email      string `json:"email"`
	Role       string `json:"role"`
	Status     int8   `json:"status"`
	AvatarURL  string `json:"avatarUrl,omitempty"`
	CreateTime string `json:"createTime"`
}
