package model

import (
	"time"
)

// FoodCategory 食物分类模型 - 完全匹配数据库表结构
type FoodCategory struct {
	ID          int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name        string    `gorm:"column:name;type:varchar(100);not null;uniqueIndex:uk_category_name;comment:分类名称" json:"name"`
	Description *string   `gorm:"column:description;type:varchar(255);comment:分类描述" json:"description,omitempty"`
	Color       *string   `gorm:"column:color;type:varchar(20);comment:分类颜色" json:"color,omitempty"`
	SortOrder   int       `gorm:"column:sort_order;default:0;comment:排序顺序" json:"sortOrder"`
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
}

// TableName 指定表名
func (FoodCategory) TableName() string {
	return "food_category"
}

// Food 食物模型 - 完全匹配数据库表结构
type Food struct {
	ID         int     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	FoodName   *string `gorm:"column:food_name;type:varchar(255)" json:"foodName,omitempty"`
	Measure    *string `gorm:"column:measure;type:varchar(100)" json:"measure,omitempty"`
	Grams      *float64 `gorm:"column:grams;type:double;comment:克数。约定：0.01 代表微量。" json:"grams,omitempty"`
	Calories   *float64 `gorm:"column:calories;type:double;comment:卡路里。约定：0.01 代表微量。" json:"calories,omitempty"`
	Protein    *float64 `gorm:"column:protein;type:double;comment:蛋白质含量(克)。约定：0.01 代表微量。" json:"protein,omitempty"`
	Fat        *float64 `gorm:"column:fat;type:double;comment:总脂肪含量(克)。约定：0.01 代表微量。" json:"fat,omitempty"`
	SatFat     *float64 `gorm:"column:sat_fat;type:double;comment:饱和脂肪含量(克)。约定：0.01 代表微量。" json:"satFat,omitempty"`
	Fiber      *string  `gorm:"column:fiber;type:varchar(20)" json:"fiber,omitempty"`
	Carbs      *float64 `gorm:"column:carbs;type:double;comment:碳水化合物含量(克)。约定：0.01 代表微量。" json:"carbs,omitempty"`
	ImageURL   *string  `gorm:"column:image_url;type:varchar(255);comment:食物图片URL" json:"imageUrl,omitempty"`
	CategoryID *int     `gorm:"column:category_id;index:idx_category_id;comment:分类ID" json:"categoryId,omitempty"`
	
	// 关联关系
	Category *FoodCategory `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"`
}

// TableName 指定表名
func (Food) TableName() string {
	return "food"
}
