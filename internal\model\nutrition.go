package model

import (
	"time"
)

// NutritionAdvice 营养建议配置表模型 - 完全匹配数据库表结构
type NutritionAdvice struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Type          string    `gorm:"column:type;type:varchar(20);not null;comment:建议类型: warning, info, danger, success" json:"type"`
	Title         string    `gorm:"column:title;type:varchar(100);not null;comment:建议标题" json:"title"`
	Description   string    `gorm:"column:description;type:varchar(500);not null;comment:建议详情" json:"description"`
	ConditionType string    `gorm:"column:condition_type;type:varchar(20);not null;index:idx_condition_type;comment:条件类型: protein, carbs, fat, calorie" json:"conditionType"`
	MinPercentage *int      `gorm:"column:min_percentage;comment:最小百分比阈值" json:"minPercentage,omitempty"`
	MaxPercentage *int      `gorm:"column:max_percentage;comment:最大百分比阈值" json:"maxPercentage,omitempty"`
	IsDefault     bool      `gorm:"column:is_default;type:tinyint(1);not null;default:0;comment:是否为默认建议" json:"isDefault"`
	Priority      int       `gorm:"column:priority;not null;default:0;comment:优先级，数字越大优先级越高" json:"priority"`
	Status        bool      `gorm:"column:status;type:tinyint(1);not null;default:1;index:idx_status;comment:状态：1-启用，0-禁用" json:"status"`
	CreatedAt     time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
}

// TableName 指定表名
func (NutritionAdvice) TableName() string {
	return "nutrition_advice"
}


