package service

import (
	"errors"
	"fmt"
	"strings"

	"golang.org/x/crypto/bcrypt"

	"shikeyinxiang/internal/domain/entities"
	"shikeyinxiang/internal/domain/repositories"
	"shikeyinxiang/internal/dto"
	"shikeyinxiang/internal/infrastructure/auth"
)

// AuthServiceImpl 认证应用服务实现
type AuthServiceImpl struct {
	userRepo repositories.UserRepository
}

// NewAuthService 创建认证服务实例
func NewAuthService(userRepo repositories.UserRepository) *AuthServiceImpl {
	return &AuthServiceImpl{
		userRepo: userRepo,
	}
}

// Login 用户登录
func (a *AuthServiceImpl) Login(loginRequest *dto.LoginRequest) (*dto.LoginResponse, error) {
	// 查找用户（支持用户名或邮箱登录）
	user, err := a.userRepo.GetByUsernameOrEmail(loginRequest.Username)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, errors.New("用户已被禁用")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginRequest.Password)); err != nil {
		return nil, errors.New("密码错误")
	}

	// 生成JWT令牌
	token, err := auth.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	// 构建响应
	userInfo := &dto.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		CreateTime: user.CreateTime,
	}

	if user.AvatarURL != nil {
		userInfo.AvatarURL = *user.AvatarURL
	}

	return &dto.LoginResponse{
		User:  userInfo,
		Token: token,
	}, nil
}

// Register 用户注册
func (a *AuthServiceImpl) Register(registerRequest *dto.RegisterRequest) (*dto.UserInfo, error) {
	// 检查用户名是否已存在
	exists, err := a.userRepo.ExistsByUsername(registerRequest.Username)
	if err != nil {
		return nil, fmt.Errorf("检查用户名失败: %w", err)
	}
	if exists {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	exists, err = a.userRepo.ExistsByEmail(registerRequest.Email)
	if err != nil {
		return nil, fmt.Errorf("检查邮箱失败: %w", err)
	}
	if exists {
		return nil, errors.New("邮箱已被注册")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(registerRequest.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建新用户实体
	newUser := entities.NewUser(
		registerRequest.Username,
		registerRequest.Email,
		string(hashedPassword),
		"USER", // 默认角色为普通用户
	)

	// 保存到数据库
	if err := a.userRepo.Create(newUser); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 构建响应（不包含密码）
	return &dto.UserInfo{
		ID:         newUser.ID,
		Username:   newUser.Username,
		Email:      newUser.Email,
		Role:       newUser.Role,
		Status:     newUser.Status,
		CreateTime: newUser.CreateTime,
	}, nil
}

// Logout 用户登出
func (a *AuthServiceImpl) Logout(token string) error {
	// 移除Bearer前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 将令牌加入黑名单
	if err := auth.BlacklistToken(token); err != nil {
		return fmt.Errorf("登出失败: %w", err)
	}

	return nil
}

// RefreshToken 刷新令牌
func (a *AuthServiceImpl) RefreshToken(token string) (string, error) {
	// 移除Bearer前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 解析当前令牌
	claims, err := auth.ParseToken(token)
	if err != nil {
		return "", fmt.Errorf("令牌解析失败: %w", err)
	}

	// 验证用户是否仍然存在且状态正常
	user, err := a.userRepo.GetByID(claims.UserID)
	if err != nil {
		return "", fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil || !user.IsActive() {
		return "", errors.New("用户不存在或已被禁用")
	}

	// 将旧令牌加入黑名单
	if err := auth.BlacklistToken(token); err != nil {
		return "", fmt.Errorf("旧令牌失效失败: %w", err)
	}

	// 生成新令牌
	newToken, err := auth.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return "", fmt.Errorf("生成新令牌失败: %w", err)
	}

	return newToken, nil
}
