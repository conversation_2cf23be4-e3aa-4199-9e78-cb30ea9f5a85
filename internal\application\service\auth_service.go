package service

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"shikeyinxiang/internal/domain/service"
	"shikeyinxiang/internal/infrastructure/auth"
	"shikeyinxiang/internal/infrastructure/database"
	"shikeyinxiang/internal/model"
)

// AuthServiceImpl 认证服务实现
type AuthServiceImpl struct {
	db *gorm.DB
}

// NewAuthService 创建认证服务实例
func NewAuthService() service.AuthService {
	return &AuthServiceImpl{
		db: database.GetDB(),
	}
}

// Login 用户登录
func (a *AuthServiceImpl) Login(loginRequest *service.LoginRequest) (*service.LoginResponse, error) {
	// 查找用户（支持用户名或邮箱登录）
	var user model.User
	query := a.db.Where("username = ? OR email = ?", loginRequest.Username, loginRequest.Username)
	
	if err := query.First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, businessErrors.ErrUserNotFound
		}
		return nil, businessErrors.WrapError(err, 500, "查询用户失败")
	}

	// 检查用户状态
	if user.Status != 1 {
		return nil, businessErrors.ErrUserDisabled
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginRequest.Password)); err != nil {
		return nil, businessErrors.ErrInvalidPassword
	}

	// 生成JWT令牌
	token, err := auth.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}

	// 构建响应
	userInfo := &service.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		CreateTime: user.CreateTime.Format("2006-01-02 15:04:05"),
	}

	if user.AvatarURL != nil {
		userInfo.AvatarURL = *user.AvatarURL
	}

	return &service.LoginResponse{
		User:  userInfo,
		Token: token,
	}, nil
}

// Register 用户注册
func (a *AuthServiceImpl) Register(registerRequest *service.RegisterRequest) (*model.User, error) {
	// 检查用户名是否已存在
	var existingUser model.User
	if err := a.db.Where("username = ?", registerRequest.Username).First(&existingUser).Error; err == nil {
		return nil, fmt.Errorf("用户名已存在")
	}

	// 检查邮箱是否已存在
	if err := a.db.Where("email = ?", registerRequest.Email).First(&existingUser).Error; err == nil {
		return nil, fmt.Errorf("邮箱已被注册")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(registerRequest.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建新用户
	newUser := model.User{
		Username:   registerRequest.Username,
		Email:      registerRequest.Email,
		Password:   string(hashedPassword),
		Role:       "USER", // 默认角色为普通用户
		Status:     1,      // 默认启用状态
		CreateTime: time.Now(),
	}

	// 保存到数据库
	if err := a.db.Create(&newUser).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 清除密码字段
	newUser.Password = ""
	return &newUser, nil
}

// Logout 用户登出
func (a *AuthServiceImpl) Logout(token string) error {
	// 移除Bearer前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 将令牌加入黑名单
	if err := auth.BlacklistToken(token); err != nil {
		return fmt.Errorf("登出失败: %w", err)
	}

	return nil
}

// RefreshToken 刷新令牌
func (a *AuthServiceImpl) RefreshToken(token string) (string, error) {
	// 移除Bearer前缀
	if strings.HasPrefix(token, "Bearer ") {
		token = strings.TrimPrefix(token, "Bearer ")
	}

	// 解析当前令牌
	claims, err := auth.ParseToken(token)
	if err != nil {
		return "", fmt.Errorf("令牌解析失败: %w", err)
	}

	// 验证用户是否仍然存在且状态正常
	var user model.User
	if err := a.db.Where("id = ? AND status = 1", claims.UserID).First(&user).Error; err != nil {
		return "", fmt.Errorf("用户不存在或已被禁用")
	}

	// 将旧令牌加入黑名单
	if err := auth.BlacklistToken(token); err != nil {
		return "", fmt.Errorf("旧令牌失效失败: %w", err)
	}

	// 生成新令牌
	newToken, err := auth.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return "", fmt.Errorf("生成新令牌失败: %w", err)
	}

	return newToken, nil
}
