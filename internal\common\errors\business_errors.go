package errors

import "errors"

// 定义业务错误
var (
	// 用户相关错误
	ErrUserNotFound     = errors.New("用户不存在")
	ErrUserDisabled     = errors.New("用户已被禁用")
	ErrInvalidPassword  = errors.New("密码错误")
	ErrUsernameExists   = errors.New("用户名已存在")
	ErrEmailExists      = errors.New("邮箱已被注册")
	
	// 认证相关错误
	ErrTokenInvalid     = errors.New("令牌无效")
	ErrTokenExpired     = errors.New("令牌已过期")
	ErrTokenBlacklisted = errors.New("令牌已被列入黑名单")
	
	// 权限相关错误
	ErrUnauthorized     = errors.New("未授权访问")
	ErrForbidden        = errors.New("权限不足")
	
	// 参数相关错误
	ErrInvalidParams    = errors.New("参数错误")
	ErrMissingParams    = errors.New("缺少必要参数")
	
	// 系统相关错误
	ErrInternalServer   = errors.New("服务器内部错误")
	ErrDatabaseError    = errors.New("数据库操作失败")
)

// BusinessError 业务错误结构
type BusinessError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Err     error  `json:"-"`
}

// Error 实现 error 接口
func (e *BusinessError) Error() string {
	if e.Err != nil {
		return e.Message + ": " + e.Err.Error()
	}
	return e.Message
}

// Unwrap 支持错误链
func (e *BusinessError) Unwrap() error {
	return e.Err
}

// NewBusinessError 创建业务错误
func NewBusinessError(code int, message string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
	}
}

// WrapError 包装错误
func WrapError(err error, code int, message string) *BusinessError {
	return &BusinessError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// 预定义的业务错误实例
var (
	BusinessErrUserNotFound     = NewBusinessError(1001, "用户不存在")
	BusinessErrUserDisabled     = NewBusinessError(1002, "用户已被禁用")
	BusinessErrInvalidPassword  = NewBusinessError(1003, "密码错误")
	BusinessErrUsernameExists   = NewBusinessError(1004, "用户名已存在")
	BusinessErrEmailExists      = NewBusinessError(1005, "邮箱已被注册")
	BusinessErrTokenInvalid     = NewBusinessError(2001, "令牌无效")
	BusinessErrTokenExpired     = NewBusinessError(2002, "令牌已过期")
	BusinessErrUnauthorized     = NewBusinessError(2003, "未授权访问")
	BusinessErrForbidden        = NewBusinessError(2004, "权限不足")
	BusinessErrInvalidParams    = NewBusinessError(3001, "参数错误")
	BusinessErrInternalServer   = NewBusinessError(5001, "服务器内部错误")
)
