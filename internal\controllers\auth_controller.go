package controllers

import (
	"github.com/gin-gonic/gin"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/service"
)

// AuthController 认证控制器
type AuthController struct{}

// NewAuthController 创建认证控制器实例
func NewAuthController() *AuthController {
	return &AuthController{}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户使用用户名/邮箱和密码登录系统
// @Tags 认证
// @Accept json
// @Produce json
// @Param loginRequest body v1.LoginReq true "登录请求"
// @Success 200 {object} response.ApiResponse{data=v1.LoginRes} "登录成功"
// @Failure 400 {object} response.ApiResponse "参数错误"
// @Failure 401 {object} response.ApiResponse "认证失败"
// @Router /api/auth/user/login [post]
func (ac *AuthController) Login(c *gin.Context) {
	var req v1.LoginReq

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage(c, 400, "参数格式错误: "+err.Error())
		return
	}

	// 执行登录
	res, err := service.Auth.Login(c.Request.Context(), &req)
	if err != nil {
		ac.handleAuthError(c, err)
		return
	}

	// 返回成功响应
	response.Success(c, res)
}

// Register 用户注册
// @Summary 用户注册
// @Description 新用户注册账号
// @Tags 认证
// @Accept json
// @Produce json
// @Param registerRequest body v1.RegisterReq true "注册请求"
// @Success 200 {object} response.ApiResponse{data=v1.RegisterRes} "注册成功"
// @Failure 400 {object} response.ApiResponse "参数错误"
// @Failure 409 {object} response.ApiResponse "用户已存在"
// @Router /api/auth/register [post]
func (ac *AuthController) Register(c *gin.Context) {
	var req v1.RegisterReq

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage(c, 400, "参数格式错误: "+err.Error())
		return
	}

	// 执行注册
	res, err := service.Auth.Register(c.Request.Context(), &req)
	if err != nil {
		ac.handleAuthError(c, err)
		return
	}

	// 返回成功响应
	response.Success(c, res)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出系统，令牌将被加入黑名单
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.ApiResponse "登出成功"
// @Failure 401 {object} response.ApiResponse "未授权"
// @Failure 500 {object} response.ApiResponse "服务器错误"
// @Router /api/auth/logout [post]
func (ac *AuthController) Logout(c *gin.Context) {
	// 获取Authorization头
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		response.ErrorWithMessage(c, 401, "缺少认证令牌")
		return
	}

	// 执行登出
	if err := service.Auth.Logout(c.Request.Context(), authHeader); err != nil {
		ac.handleAuthError(c, err)
		return
	}

	// 返回成功响应
	response.Success(c, gin.H{"message": "登出成功"})
}

// RefreshToken 刷新令牌
// @Summary 刷新JWT令牌
// @Description 使用当前令牌获取新的令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.ApiResponse{data=gin.H} "刷新成功"
// @Failure 401 {object} response.ApiResponse "令牌无效"
// @Failure 500 {object} response.ApiResponse "服务器错误"
// @Router /api/auth/refresh [post]
func (ac *AuthController) RefreshToken(c *gin.Context) {
	// 获取Authorization头
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		response.ErrorWithMessage(c, 401, "缺少认证令牌")
		return
	}

	// 执行令牌刷新
	newToken, err := service.Auth.RefreshToken(c.Request.Context(), authHeader)
	if err != nil {
		ac.handleAuthError(c, err)
		return
	}

	// 返回新令牌
	response.Success(c, gin.H{
		"token": newToken,
	})
}

// GetCurrentUser 获取当前登录用户信息
// @Summary 获取当前用户信息
// @Description 根据JWT令牌获取当前登录用户的信息
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.ApiResponse{data=v1.UserInfo} "获取成功"
// @Failure 401 {object} response.ApiResponse "未授权"
// @Router /api/auth/me [get]
func (ac *AuthController) GetCurrentUser(c *gin.Context) {
	// 从中间件获取用户信息
	userID, exists := c.Get("userId")
	if !exists {
		response.ErrorWithMessage(c, 401, "用户未登录")
		return
	}

	username, _ := c.Get("username")
	role, _ := c.Get("role")

	// 构建用户信息响应
	userInfo := v1.UserInfo{
		ID:       userID.(int64),
		Username: username.(string),
		Role:     role.(string),
	}

	response.Success(c, userInfo)
}

// handleAuthError 统一处理认证相关错误
func (ac *AuthController) handleAuthError(c *gin.Context, err error) {
	switch err.Error() {
	case consts.MsgUserNotFound:
		response.ErrorWithMessage(c, consts.CodeUserNotFound, err.Error())
	case consts.MsgPasswordError:
		response.ErrorWithMessage(c, consts.CodePasswordError, err.Error())
	case consts.MsgUserDisabled:
		response.ErrorWithMessage(c, consts.CodeUserDisabled, err.Error())
	case consts.MsgUsernameExists:
		response.ErrorWithMessage(c, consts.CodeUsernameExists, err.Error())
	case consts.MsgEmailExists:
		response.ErrorWithMessage(c, consts.CodeEmailExists, err.Error())
	case "用户不存在或已被禁用":
		response.ErrorWithMessage(c, consts.CodeUnauthorized, err.Error())
	default:
		// 对于未知错误，使用全局错误处理
		c.Error(err)
	}
}
