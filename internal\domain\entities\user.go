package entities

import (
	"time"
	"github.com/shopspring/decimal"
)

// User 用户领域实体 - 纯净的业务对象，不包含持久化细节
type User struct {
	ID         int64
	Username   string
	Password   string // 在领域层保留，但在 DTO 中不暴露
	Email      string
	Role       string
	Status     int8
	CreateTime time.Time
	OpenID     *string
	AvatarURL  *string
}

// NewUser 创建新用户实体
func NewUser(username, email, password, role string) *User {
	return &User{
		Username:   username,
		Email:      email,
		Password:   password,
		Role:       role,
		Status:     1, // 默认启用
		CreateTime: time.Now(),
	}
}

// IsActive 检查用户是否处于活跃状态
func (u *User) IsActive() bool {
	return u.Status == 1
}

// IsAdmin 检查用户是否为管理员
func (u *User) IsAdmin() bool {
	return u.Role == "ADMIN"
}

// SetAvatarURL 设置用户头像URL
func (u *User) SetAvatarURL(url string) {
	u.AvatarURL = &url
}

// UserNutritionGoals 用户营养目标领域实体
type UserNutritionGoals struct {
	ID             int64
	UserID         int64
	CalorieTarget  *int
	WeightTarget   *decimal.Decimal
	ProteinTarget  *int
	CarbsTarget    *int
	FatTarget      *int
	IsVegetarian   bool
	IsLowCarb      bool
	IsHighProtein  bool
	IsGlutenFree   bool
	IsLowSodium    bool
	CreatedAt      *time.Time
	UpdatedAt      *time.Time
}

// NewUserNutritionGoals 创建新的用户营养目标
func NewUserNutritionGoals(userID int64) *UserNutritionGoals {
	now := time.Now()
	return &UserNutritionGoals{
		UserID:    userID,
		CreatedAt: &now,
		UpdatedAt: &now,
	}
}

// SetCalorieTarget 设置卡路里目标
func (ung *UserNutritionGoals) SetCalorieTarget(target int) {
	ung.CalorieTarget = &target
	now := time.Now()
	ung.UpdatedAt = &now
}

// SetWeightTarget 设置体重目标
func (ung *UserNutritionGoals) SetWeightTarget(target decimal.Decimal) {
	ung.WeightTarget = &target
	now := time.Now()
	ung.UpdatedAt = &now
}
