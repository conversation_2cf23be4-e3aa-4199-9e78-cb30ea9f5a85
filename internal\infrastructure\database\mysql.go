package database

import (
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"shikeyinxiang/internal/config"
	"shikeyinxiang/internal/infrastructure/persistence"
)

var DB *gorm.DB

// InitMySQL 初始化MySQL数据库连接
func InitMySQL(cfg *config.DatabaseConfig) error {
	dsn := cfg.GetDSN()
	
	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	}

	// 连接数据库
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	DB = db
	return nil
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// 自动迁移所有模型（移除了FileMetadata）
	err := DB.AutoMigrate(
		&persistence.UserModel{},
		&persistence.UserNutritionGoalsModel{},
		&persistence.FoodCategoryModel{},
		&persistence.FoodModel{},
		&persistence.DietRecordModel{},
		&model.DietRecordFood{},
		&model.NutritionAdvice{},
	)

	if err != nil {
		return fmt.Errorf("failed to auto migrate: %w", err)
	}

	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}
